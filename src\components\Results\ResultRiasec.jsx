import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import apiService from '../../services/apiService';
import EnhancedLoadingScreen from '../UI/EnhancedLoadingScreen';
import RiasecRadarChart from './RiasecRadarChart';
import AssessmentExplanations from './AssessmentExplanations';

const ResultRiasec = () => {
  const { resultId } = useParams();
  const navigate = useNavigate();
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const fetchInProgressRef = useRef(false);
  const abortControllerRef = useRef(null);

  useEffect(() => {
    // Prevent duplicate calls
    if (fetchInProgressRef.current) {
      return;
    }

    const fetchResult = async (retryCount = 0) => {
      const maxRetries = 5;
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff, max 10s

      // Create new AbortController for this fetch sequence
      abortControllerRef.current = new AbortController();

      try {
        fetchInProgressRef.current = true;
        const response = await apiService.getResultById(resultId);

        // Check if component is still mounted and request wasn't aborted
        if (!abortControllerRef.current?.signal.aborted) {
          if (response.success) {
            setResult(response.data);
            fetchInProgressRef.current = false;
          }
        }
      } catch (err) {
        // Check if the error is due to abort
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        // If it's a 404 and we haven't exceeded max retries, try again
        if (err.response?.status === 404 && retryCount < maxRetries) {
          setTimeout(() => {
            // Check if component is still mounted before retrying
            if (!abortControllerRef.current?.signal.aborted) {
              fetchResult(retryCount + 1);
            }
          }, retryDelay);
        } else {
          // Final error after all retries or non-404 error
          const errorMessage = retryCount >= maxRetries
            ? `Result not found after ${maxRetries + 1} attempts. The analysis may still be processing.`
            : err.response?.data?.message || 'Failed to load results';
          setError(errorMessage);
          fetchInProgressRef.current = false;
        }
      }
    };

    if (resultId) {
      fetchResult();
    } else {
      navigate('/dashboard');
    }

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      fetchInProgressRef.current = false;
    };
  }, [resultId, navigate]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };



  const getRiasecDescription = (type) => {
    const descriptions = {
      'realistic': 'Prefer hands-on, practical work with tools, machines, or animals. Value concrete results and physical activities.',
      'investigative': 'Enjoy analyzing, researching, and solving complex problems. Prefer intellectual challenges and scientific thinking.',
      'artistic': 'Value creativity, self-expression, and aesthetic experiences. Prefer unstructured environments and original work.',
      'social': 'Enjoy helping, teaching, and working with people. Value cooperation, understanding, and making a positive impact.',
      'enterprising': 'Like leading, persuading, and managing others. Value achievement, competition, and business success.',
      'conventional': 'Prefer organized, structured work with clear procedures. Value accuracy, efficiency, and systematic approaches.'
    };
    return descriptions[type] || 'A valuable career interest area';
  };

  const getRiasecCareers = (type) => {
    const careers = {
      'realistic': ['Engineer', 'Mechanic', 'Carpenter', 'Farmer', 'Pilot', 'Chef'],
      'investigative': ['Scientist', 'Researcher', 'Doctor', 'Analyst', 'Psychologist', 'Programmer'],
      'artistic': ['Designer', 'Writer', 'Musician', 'Actor', 'Photographer', 'Architect'],
      'social': ['Teacher', 'Counselor', 'Social Worker', 'Nurse', 'Therapist', 'Coach'],
      'enterprising': ['Manager', 'Sales Representative', 'Entrepreneur', 'Lawyer', 'Marketing Director', 'CEO'],
      'conventional': ['Accountant', 'Administrator', 'Banker', 'Secretary', 'Data Analyst', 'Librarian']
    };
    return careers[type] || [];
  };

  // Navigation cards data
  const navigationCards = [
    {
      title: 'Character Strengths',
      subtitle: 'VIA-IS Assessment',
      description: 'Explore your core character strengths and values',
      path: `/results/${resultId}/via-is`
    },
    {
      title: 'Personality Traits',
      subtitle: 'OCEAN Assessment',
      description: 'Understand your personality dimensions',
      path: `/results/${resultId}/ocean`
    },
    {
      title: 'Career Persona',
      subtitle: 'Integrated Profile',
      description: 'Your comprehensive career recommendations',
      path: `/results/${resultId}/persona`
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Loading State */}
        {!result && !error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <EnhancedLoadingScreen
              title="Loading RIASEC Results..."
              subtitle="Fetching your career interests analysis"
              skeletonCount={4}
              className="min-h-[600px]"
            />
          </motion.div>
        )}

        {/* Error State */}
        {error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm"
          >
            <div className="flex items-center">
              <div className="text-gray-400 mr-3">⚠️</div>
              <div>
                <h3 className="text-gray-900 font-semibold">Unable to Load Results</h3>
                <p className="text-gray-600 text-sm mt-1">{error}</p>
                <div className="mt-4 space-x-3">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-gray-900 text-white px-4 py-2 rounded-md text-sm hover:bg-gray-800 transition-colors"
                  >
                    Retry
                  </button>
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-200 transition-colors"
                  >
                    Back to Overview
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Content State */}
        {result && (
          <>
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-8"
            >
              <div className="flex justify-between items-start mb-6">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    RIASEC Career Interests Assessment
                  </h1>
                  <p className="text-gray-600 max-w-2xl">
                    Discover your natural career interests and work environments that align with your personality.
                    The RIASEC model identifies six key interest areas that guide career satisfaction and success.
                  </p>
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                  >
                    ← Back
                  </button>
                  <button
                    onClick={() => navigate('/dashboard')}
                    className="px-4 py-2 bg-gray-900 text-white rounded-md hover:bg-gray-800 transition-colors"
                  >
                    Dashboard
                  </button>
                </div>
              </div>

              <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-gray-900 rounded-full mr-2"></span>
                    Completed: {formatDate(result.created_at)}
                  </div>
                  <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-xs font-medium">
                    RIASEC Model
                  </span>
                </div>
              </div>
            </motion.div>

            {/* Introduction Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-8"
            >
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 p-6">
                  <div className="flex items-center mb-4">
                    <span className="text-3xl mr-3">🎯</span>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">Understanding Your Career Interests</h2>
                      <p className="text-blue-700 font-medium">The RIASEC Career Interest Model</p>
                    </div>
                  </div>
                  <p className="text-gray-700 leading-relaxed mb-4">
                    The RIASEC model, developed by psychologist John Holland, identifies six personality types
                    that correspond to different work environments and career paths. Your results show how
                    strongly you align with each type.
                  </p>

                  <div className="grid md:grid-cols-2 gap-4 mb-4">
                    <div className="bg-white p-4 rounded-lg border border-blue-100">
                      <h4 className="font-semibold text-gray-900 mb-2">👨‍🔬 Developer</h4>
                      <p className="text-sm text-gray-700">Developed by psychologist John Holland (1959) and refined through decades of research</p>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-blue-100">
                      <h4 className="font-semibold text-gray-900 mb-2">✅ Scientific Validity</h4>
                      <p className="text-sm text-gray-700">Extensively validated across cultures and used worldwide for career counseling and assessment</p>
                    </div>
                  </div>

                  <div className="bg-white p-4 rounded-lg border border-blue-100">
                    <h4 className="font-semibold text-gray-900 mb-2">🎯 Assessment Purpose</h4>
                    <p className="text-sm text-gray-700">
                      Identifies career interests and work environments that align with your personality, helping you find greater
                      satisfaction, engagement, and success in your career choices.
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* RIASEC Chart */}
            {result.assessment_data?.riasec && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="mb-8"
              >
                <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Your Interest Profile</h3>
                    <p className="text-gray-600">Visual representation of your career interest strengths</p>
                  </div>
                  <RiasecRadarChart data={result.assessment_data.riasec} />
                </div>
              </motion.div>
            )}

            {/* Career Interests Analysis */}
            {result.assessment_data?.riasec && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
                className="mb-8"
              >
                <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">Your RIASEC Interest Profile</h3>
                    <p className="text-gray-600">Detailed breakdown of your career interest areas</p>
                  </div>

                  {/* RIASEC Interest Cards - 3 columns 2 rows */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {Object.entries(result.assessment_data.riasec).map(([type, score], index) => {
                      const getScoreLevel = (score) => {
                        if (score >= 70) return { level: 'High', color: 'text-green-700' };
                        if (score >= 30) return { level: 'Medium', color: 'text-amber-700' };
                        return { level: 'Low', color: 'text-slate-600' };
                      };

                      const scoreInfo = getScoreLevel(score);

                      return (
                        <motion.div
                          key={type}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.7 + index * 0.1 }}
                          className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
                        >
                          {/* Header */}
                          <div className="flex justify-between items-start mb-3">
                            <div className="flex-1 min-w-0">
                              <h4 className="font-bold text-gray-900 capitalize text-lg truncate">{type}</h4>
                              <p className="text-xs text-gray-600 uppercase tracking-wide">
                                {type === 'realistic' && 'Doer'}
                                {type === 'investigative' && 'Thinker'}
                                {type === 'artistic' && 'Creator'}
                                {type === 'social' && 'Helper'}
                                {type === 'enterprising' && 'Persuader'}
                                {type === 'conventional' && 'Organizer'}
                              </p>
                            </div>
                            <div className="text-right flex-shrink-0 ml-2">
                              <div className="flex items-center gap-1">
                                <span className="font-bold text-gray-900 text-lg">{score.toFixed(1)}</span>
                                <span className={`text-xs font-medium ${scoreInfo.color}`}>({scoreInfo.level})</span>
                              </div>
                            </div>
                          </div>

                          {/* Description */}
                          <p className="text-gray-700 text-sm mb-4 leading-relaxed">{getRiasecDescription(type)}</p>

                          {/* Progress Bar */}
                          <div className="mb-4 w-full">
                            <div className="bg-gray-200 rounded-full h-2 w-full overflow-hidden">
                              <motion.div
                                className="bg-slate-700 h-2 rounded-full"
                                initial={{ width: 0 }}
                                animate={{ width: `${Math.min(score, 100)}%` }}
                                transition={{ duration: 1, delay: 0.8 + index * 0.1 }}
                              />
                            </div>
                          </div>

                          {/* Career Paths */}
                          <div className="bg-white rounded-lg p-3 border border-gray-100">
                            <h5 className="font-semibold text-gray-900 mb-2 text-sm">Related Careers</h5>
                            <div className="flex flex-wrap gap-1">
                              {getRiasecCareers(type).slice(0, 4).map((career, careerIdx) => (
                                <span key={careerIdx} className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full font-medium">
                                  {career}
                                </span>
                              ))}
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                </div>
              </motion.div>
            )}

            {/* Navigation to Other Results */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.0 }}
              className="mb-12"
            >
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8 mb-8">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                    <span className="text-2xl">🎯</span>
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-3">
                    Explore Your Complete Profile
                  </h2>
                  <p className="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
                    Continue your journey by exploring other aspects of your assessment results.
                    Each assessment provides unique insights into different facets of your personality and career potential.
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                {navigationCards.map((card, index) => (
                  <motion.div
                    key={card.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 1.1 + index * 0.1 }}
                    className="group cursor-pointer"
                    onClick={() => navigate(card.path)}
                  >
                    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md hover:border-gray-300 transition-all duration-200 h-full">
                      <div className="flex flex-col h-full">
                        <div className="flex items-start justify-end mb-4">
                          <svg
                            className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors duration-200"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </div>

                        <div className="flex-grow">
                          <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-gray-700 transition-colors duration-200">
                            {card.title}
                          </h3>
                          <p className="text-sm text-gray-500 mb-3 font-semibold uppercase tracking-wide">
                            {card.subtitle}
                          </p>
                          <p className="text-gray-600 leading-relaxed">
                            {card.description}
                          </p>
                        </div>

                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <div className="flex items-center text-sm font-medium text-gray-500 group-hover:text-blue-600 transition-colors duration-200">
                            <span>Explore Assessment</span>
                            <svg
                              className="w-4 h-4 ml-1"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Assessment Explanations */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 2.0 }}
            >
              <AssessmentExplanations showOnly="riasec" />
            </motion.div>
          </>
        )}
      </div>
    </div>
  );
};

export default ResultRiasec;